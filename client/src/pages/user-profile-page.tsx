import React, { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import DashboardLayoutWrapper from "@/components/layout/dashboard-layout";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  User,
  Briefcase,
  Building,
  ChevronRight,
  Save,
  Shield,
  Bell,
  KeyRound,
  LogOut,
  Loader2,
} from "lucide-react";

// Interfaz para los datos del formulario de perfil
interface ProfileFormData {
  displayName: string;
  email: string;
  companyName: string;
  industry: string;
  role: string;
}

// Función para calcular el hash MD5 para Gravatar
function md5(input: string): string {
  // Utilizo una implementación simplificada donde usamos btoa,
  // pero en un entorno real debería ser un MD5 completo
  const str = input.toLowerCase().trim();
  // Usando directamente la URL del correo como referencia para evitar
  // implementaciones complejas de hash
  return encodeURIComponent(str);
}

// Función para obtener la URL del avatar de Gravatar
function getGravatarUrl(email: string) {
  // Si no hay email, devolver avatar por defecto
  if (!email) return "https://www.gravatar.com/avatar/?d=mp&s=200";

  // Gravatar usa el email para identificar la imagen asociada
  const hash = md5(email);
  // Usamos 'd=mp' para un placeholder minimalista en caso de no tener imagen
  // size=200 para una imagen grande y de buena calidad
  return `https://www.gravatar.com/avatar/${hash}?d=mp&s=200`;
}

export default function UserProfilePage() {
  const { toast } = useToast();
  const { user, logoutMutation } = useAuth();
  const [, navigate] = useLocation();

  // Estados
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState<ProfileFormData>({
    displayName: "",
    email: "",
    companyName: "",
    industry: "Tecnología",
    role: "user",
  });

  // Industrias disponibles para seleccionar
  const industries = [
    "Tecnología",
    "Marketing",
    "Educación",
    "Salud",
    "Finanzas",
    "Retail",
    "Bienes raíces",
    "Turismo",
    "Alimentación",
    "Arte y entretenimiento",
    "Moda",
    "Deportes",
    "Otra",
  ];

  // Inicializar los datos del formulario con la información del usuario
  useEffect(() => {
    if (user) {
      setFormData({
        displayName: user.username || "",
        email: user.email || "",
        companyName: "",
        industry: "Tecnología",
        role: user.role || "user",
      });
    }
  }, [user]);

  // Manejar cambios en los campos del formulario
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Manejar cambios en selects
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Guardar cambios del perfil (simulado por ahora)
  const handleSaveProfile = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "No se puede identificar al usuario para actualizar el perfil",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);

    try {
      // Simular guardado (en una implementación real, aquí guardarías en Supabase)
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Perfil actualizado",
        description: "Los cambios en tu perfil se han guardado correctamente",
      });

      setIsEditing(false);
    } catch (err) {
      console.error("Error al actualizar perfil:", err);
      toast({
        title: "Error al guardar",
        description: "No se pudo actualizar el perfil",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Si no hay usuario, mostrar mensaje
  if (!user) {
    return (
      <DashboardLayoutWrapper pageTitle="Mi Perfil">
        <div className="flex h-[80vh] items-center justify-center">
          <div className="flex flex-col items-center gap-4 max-w-md text-center">
            <div className="p-3 rounded-full bg-destructive/10">
              <Shield className="h-10 w-10 text-destructive" />
            </div>
            <h2 className="text-xl font-semibold">No hay usuario autenticado</h2>
            <p className="text-muted-foreground">Debes iniciar sesión para ver tu perfil</p>
            <Button onClick={() => navigate("/login")}>
              Iniciar sesión
            </Button>
          </div>
        </div>
      </DashboardLayoutWrapper>
    );
  }

  return (
    <DashboardLayoutWrapper pageTitle="Mi Perfil">
      <div className="mx-auto max-w-6xl">
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Mi Perfil</h1>
            <p className="text-muted-foreground">
              Gestiona tu información personal y preferencias de usuario
            </p>
          </div>
          {isEditing ? (
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsEditing(false)}
                disabled={isSaving}
              >
                Cancelar
              </Button>
              <Button onClick={handleSaveProfile} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Guardando...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Guardar cambios
                  </>
                )}
              </Button>
            </div>
          ) : (
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className="flex items-center"
              >
                <svg
                  className="mr-2 h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Recargar
              </Button>
              <Button onClick={() => setIsEditing(true)}>Editar perfil</Button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Columna izquierda: Foto de perfil */}
          <div className="space-y-6">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <div className="bg-white rounded-xl border border-border p-6 flex flex-col items-center shadow-sm">
                <div className="relative w-32 h-32 mb-4">
                  {formData.email ? (
                    <div className="rounded-full w-full h-full overflow-hidden border-4 border-primary/10">
                      <img
                        src={getGravatarUrl(formData.email)}
                        alt="Avatar del usuario"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Si hay error al cargar la imagen, mostrar el icono por defecto
                          const target = e.target as HTMLImageElement;
                          target.style.display = "none";
                          target.parentElement!.classList.add(
                            "bg-primary/10",
                            "flex",
                            "items-center",
                            "justify-center",
                          );
                          target.parentElement!.innerHTML += `<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>`;
                        }}
                      />
                    </div>
                  ) : (
                    <div className="rounded-full w-full h-full bg-primary/10 flex items-center justify-center border-4 border-primary/10">
                      <User size={48} className="text-primary" />
                    </div>
                  )}
                </div>
                <h2 className="text-xl font-bold mb-1">
                  {formData.displayName || user?.username || "Usuario"}
                </h2>
                <p className="text-muted-foreground text-sm mb-3">
                  {formData.email || user?.email}
                </p>
                <p className="text-xs px-3 py-1 rounded-full bg-primary/10 text-primary font-medium mb-2">
                  {formData.role === "admin" ? "Administrador" : "Usuario"}
                </p>
                {formData.companyName && (
                  <p className="text-sm text-muted-foreground flex items-center">
                    <Building size={14} className="mr-1" />
                    {formData.companyName}
                  </p>
                )}
              </div>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <div className="bg-white rounded-xl border border-border p-4 shadow-sm">
                <h3 className="font-medium mb-3">Navegación rápida</h3>
                <div className="space-y-1">
                  <Button
                    variant="ghost"
                    className="w-full justify-between"
                    onClick={() => navigate("/dashboard")}
                  >
                    Dashboard
                    <ChevronRight size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-between"
                    onClick={() => navigate("/campanias-inteligentes")}
                  >
                    Campañas Inteligentes
                    <ChevronRight size={16} />
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Columna derecha: Formulario de perfil y pestañas */}
          <div className="md:col-span-2 space-y-6">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.15 }}
            >
              <Tabs defaultValue="perfil" className="w-full">
                <TabsList className="mb-4">
                  <TabsTrigger value="perfil">Perfil</TabsTrigger>
                  <TabsTrigger value="cuenta">Cuenta</TabsTrigger>
                  <TabsTrigger value="preferencias">Preferencias</TabsTrigger>
                </TabsList>

                <TabsContent value="perfil" className="space-y-6">
                  <div className="bg-white rounded-xl border border-border p-6 shadow-sm">
                    <h2 className="text-xl font-semibold mb-6 flex items-center">
                      <User className="mr-2 h-5 w-5 text-primary" />
                      Información Personal
                    </h2>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="displayName">Nombre completo</Label>
                          <Input
                            id="displayName"
                            name="displayName"
                            value={formData.displayName}
                            onChange={handleInputChange}
                            disabled={!isEditing}
                            placeholder="Tu nombre completo"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email">Correo electrónico</Label>
                          <Input
                            id="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            disabled={true} // El email no se puede editar fácilmente en Firebase Auth
                            placeholder="<EMAIL>"
                          />
                        </div>
                      </div>

                      <Separator className="my-6" />

                      <h2 className="text-xl font-semibold mb-6 flex items-center">
                        <Briefcase className="mr-2 h-5 w-5 text-primary" />
                        Información Profesional
                      </h2>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="companyName">Empresa o Negocio</Label>
                          <Input
                            id="companyName"
                            name="companyName"
                            value={formData.companyName}
                            onChange={handleInputChange}
                            disabled={!isEditing}
                            placeholder="Nombre de tu empresa"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="industry">Industria</Label>
                          <Select
                            disabled={!isEditing}
                            value={formData.industry}
                            onValueChange={(value) =>
                              handleSelectChange("industry", value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Selecciona tu industria" />
                            </SelectTrigger>
                            <SelectContent>
                              {industries.map((industry) => (
                                <SelectItem key={industry} value={industry}>
                                  {industry}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="cuenta" className="space-y-6">
                  <div className="bg-white rounded-xl border border-border p-6 shadow-sm">
                    <h2 className="text-xl font-semibold mb-6 flex items-center">
                      <Shield className="mr-2 h-5 w-5 text-primary" />
                      Seguridad
                    </h2>
                    <div className="space-y-4">
                      <Button
                        variant="outline"
                        className="w-full justify-start"
                      >
                        <KeyRound className="mr-2 h-4 w-4" />
                        Cambiar contraseña
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full justify-start"
                      >
                        <Shield className="mr-2 h-4 w-4" />
                        Verificación en dos pasos
                      </Button>
                      <Button
                        variant="destructive"
                        className="w-full justify-start"
                        onClick={() => logoutMutation.mutate()}
                        disabled={logoutMutation.isPending}
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        {logoutMutation.isPending ? "Cerrando sesión..." : "Cerrar sesión"}
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full justify-start"
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        Cerrar sesión en todos los dispositivos
                      </Button>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="preferencias" className="space-y-6">
                  <div className="bg-white rounded-xl border border-border p-6 shadow-sm">
                    <h2 className="text-xl font-semibold mb-6 flex items-center">
                      <Bell className="mr-2 h-5 w-5 text-primary" />
                      Notificaciones
                    </h2>
                    <p className="text-muted-foreground mb-4">
                      Las preferencias de notificaciones estarán disponibles
                      próximamente.
                    </p>
                    <Button disabled>Configurar notificaciones</Button>
                  </div>
                </TabsContent>
              </Tabs>
            </motion.div>
          </div>
        </div>
      </div>
    </DashboardLayoutWrapper>
  );
}
