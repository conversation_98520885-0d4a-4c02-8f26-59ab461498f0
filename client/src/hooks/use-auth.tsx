import {
  createContext,
  ReactN<PERSON>,
  useContext,
  useState,
  useEffect,
} from "react";
import { useMutation, UseMutationResult } from "@tanstack/react-query";
import { insertUserSchema, User as AppUser, InsertUser } from "@shared/schema";
import { queryClient } from "../lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import type { User as SupabaseUser, AuthError } from "@supabase/supabase-js";

// Supabase User type for authentication
type AuthUser = SupabaseUser;

// Simplified and consolidated auth context type
type AuthContextType = {
  // User access
  user: AppUser | null;
  isLoading: boolean;
  error: Error | null;

  // Use-case-specific methods
  loginMutation: UseMutationResult<AppUser, Error, LoginData>;
  logoutMutation: UseMutationResult<void, Error, void>;
  registerMutation: UseMutationResult<AppUser, Error, InsertUser>;
  googleLoginMutation: UseMutationResult<AppUser, Error, void>;

  // Legacy support (temporary)
  currentUser: AuthUser | null;
  loading: boolean;
};

type LoginData = {
  username: string;
  password: string;
};

// Single auth context
export const AuthContext = createContext<AuthContextType | null>(null);

// Utility function to transform Supabase user to app user
function createAppUserFromSupabase(supabaseUser: AuthUser): AppUser {
  return {
    id: parseInt(supabaseUser.id.substring(0, 8), 16), // Convert part of UID to a number
    username:
      supabaseUser.user_metadata?.full_name ||
      supabaseUser.user_metadata?.name ||
      supabaseUser.email?.split("@")[0] ||
      "Usuario",
    email: supabaseUser.email || "",
    role: "user", // Default to user role
    isActive: true,
  } as AppUser;
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const [currentUser, setCurrentUser] = useState<AuthUser | null>(null);
  const [appUser, setAppUser] = useState<AppUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Supabase auth state management
  useEffect(() => {
    console.log("Setting up Supabase auth state");

    // Get initial session
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      if (error) {
        console.error("Error getting session:", error);
        setError(new Error(error.message));
      }

      if (session?.user) {
        console.log("Initial session found:", session.user.email);
        setCurrentUser(session.user);
        setAppUser(createAppUserFromSupabase(session.user));

        // If we're on the login page and have a session, redirect to dashboard
        if (window.location.pathname === '/login' || window.location.pathname === '/register') {
          window.location.href = '/dashboard';
        }
      }
      setIsLoading(false);
    }).catch((error) => {
      console.error("Session error:", error);
      setError(new Error(error.message));
      setIsLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("Auth state changed:", event, session?.user?.email);

      if (event === 'SIGNED_IN' && session?.user) {
        setCurrentUser(session.user);
        setAppUser(createAppUserFromSupabase(session.user));

        // Redirect to dashboard after successful OAuth
        if (window.location.pathname === '/login' || window.location.pathname === '/register' || window.location.pathname === '/') {
          setTimeout(() => {
            window.location.href = '/dashboard';
          }, 100);
        }
      } else if (event === 'SIGNED_OUT') {
        setCurrentUser(null);
        setAppUser(null);
      } else if (session?.user) {
        setCurrentUser(session.user);
        setAppUser(createAppUserFromSupabase(session.user));
      } else {
        setCurrentUser(null);
        setAppUser(null);
      }
      setIsLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  // Login mutation with Supabase
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginData) => {
      try {
        console.log("Attempting Supabase login");

        const { data, error } = await supabase.auth.signInWithPassword({
          email: credentials.username,
          password: credentials.password,
        });

        if (error) {
          throw error;
        }

        if (!data.user) {
          throw new Error("No user returned from authentication");
        }

        return createAppUserFromSupabase(data.user);
      } catch (error: any) {
        console.error("Login error:", error);

        let errorMessage = "Authentication failed";

        // Extract meaningful error messages from Supabase errors
        if (error.message) {
          switch (error.message) {
            case "Invalid login credentials":
              errorMessage = "Invalid email or password";
              break;
            case "Email not confirmed":
              errorMessage = "Please check your email and confirm your account";
              break;
            case "Too many requests":
              errorMessage = "Too many failed login attempts. Please try again later";
              break;
            default:
              errorMessage = error.message || "Authentication error";
          }
        }

        throw new Error(errorMessage);
      }
    },
    onSuccess: (data) => {
      toast({
        title: "Login successful!",
        description: `Welcome back, ${data.username}`,
      });

      // Forzar navegación al dashboard después del login exitoso
      console.log(
        "Forzando navegación al dashboard desde loginMutation.onSuccess",
      );
      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 500);
    },
    onError: (error: Error) => {
      toast({
        title: "Login error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Registration mutation with Supabase
  const registerMutation = useMutation({
    mutationFn: async (userData: InsertUser) => {
      try {
        console.log("Attempting Supabase registration");

        const { data, error } = await supabase.auth.signUp({
          email: userData.email,
          password: userData.password || "defaultPassword123", // Fallback password
          options: {
            data: {
              full_name: userData.username,
              username: userData.username,
            },
          },
        });

        if (error) {
          throw error;
        }

        if (!data.user) {
          throw new Error("No user returned from registration");
        }

        return createAppUserFromSupabase(data.user);
      } catch (error: any) {
        console.error("Registration error:", error);

        let errorMessage = "Registration failed";

        // Extract meaningful error messages from Supabase errors
        if (error.message) {
          switch (error.message) {
            case "User already registered":
              errorMessage = "This email is already registered";
              break;
            case "Password should be at least 6 characters":
              errorMessage = "Password is too weak. Please use a stronger password";
              break;
            case "Unable to validate email address: invalid format":
              errorMessage = "Invalid email format";
              break;
            default:
              errorMessage = error.message || "Registration error";
          }
        }

        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      toast({
        title: "Registration successful!",
        description: "Your account has been created",
      });

      // Forzar navegación al dashboard después del registro exitoso
      console.log(
        "Forzando navegación al dashboard desde registerMutation.onSuccess",
      );
      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 500);
    },
    onError: (error: Error) => {
      toast({
        title: "Registration error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Google login mutation with Supabase
  const googleLoginMutation = useMutation({
    mutationFn: async () => {
      try {
        console.log("Attempting Google login with Supabase");

        const { data, error } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: `${window.location.origin}/dashboard`,
            queryParams: {
              access_type: 'offline',
              prompt: 'consent',
            },
          },
        });

        if (error) {
          throw error;
        }

        // Note: For OAuth, the user will be redirected and the auth state will be handled by the auth listener
        // We return a placeholder user here, but the real user will be set by the auth state listener
        return {
          id: 0,
          username: "Loading...",
          email: "<EMAIL>",
          role: "user",
          isActive: true,
        } as AppUser;
      } catch (error: any) {
        console.error("Google login error:", error);
        throw new Error(error.message || "Google authentication failed");
      }
    },
    onSuccess: () => {
      toast({
        title: "Redirecting...",
        description: "Redirecting to Google for authentication",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Google login error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Logout mutation with Supabase
  const logoutMutation = useMutation({
    mutationFn: async () => {
      console.log("Logging out with Supabase");

      try {
        const { error } = await supabase.auth.signOut();
        if (error) {
          throw error;
        }
      } catch (error: any) {
        console.error("Logout error:", error);
        // Continue with logout even if there's an error
      }
    },
    onSuccess: () => {
      toast({
        title: "Sesión cerrada",
        description: "Has cerrado sesión exitosamente",
      });
      // Redirect to login page after successful logout
      window.location.href = "/login";
    },
    onError: (error: Error) => {
      toast({
        title: "Error al cerrar sesión",
        description: error.message,
        variant: "destructive",
      });
      // Even on error, redirect to login
      window.location.href = "/login";
    },
    onSuccess: () => {
      // Clear any cached user data
      setAppUser(null);
      queryClient.invalidateQueries({ queryKey: ["/api/auth/me"] });

      toast({
        title: "Logged out",
        description: "You have been successfully logged out",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Logout error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return (
    <AuthContext.Provider
      value={{
        // New API
        user: appUser,
        isLoading,
        error,
        loginMutation,
        logoutMutation,
        registerMutation,
        googleLoginMutation,

        // Legacy support
        currentUser,
        loading: isLoading,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
